package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;

import hk.org.ha.sc3.sybasechatops.service.JasyptEncryptionService;

/**
 * Unit tests for the JasyptEncryptionService class
 * Note: These tests require Spring context to run properly
 */
@SpringBootTest
public class JasyptEncryptionServiceTest {

    @Autowired
    private JasyptEncryptionService encryptionService;

    @Test
    public void testEncryptDecrypt() {
        // Skip test if service is not available (e.g., in environments without proper jasypt config)
        if (encryptionService == null) {
            return;
        }
        
        String originalText = "abcd1234";
        String encryptedText = encryptionService.encrypt(originalText);
        String decryptedText = encryptionService.decrypt(encryptedText);
        
        assertEquals(originalText, decryptedText);
        assertTrue(encryptionService.isEncrypted(encryptedText));
        assertFalse(encryptionService.isEncrypted(originalText));
    }

    @Test
    public void testIsEncrypted() {
        assertTrue(encryptionService.isEncrypted("ENC(someEncryptedValue)"));
        assertFalse(encryptionService.isEncrypted("plaintext"));
        assertFalse(encryptionService.isEncrypted(""));
        assertFalse(encryptionService.isEncrypted(null));
    }

    @Test
    public void testDecryptIfEncrypted() {
        // Skip test if service is not available
        if (encryptionService == null) {
            return;
        }

        // Test with encrypted text
        String originalText = "testPassword123";
        String encryptedText = encryptionService.encrypt(originalText);
        String result = encryptionService.decryptIfEncrypted(encryptedText);
        assertEquals(originalText, result);

        // Test with plain text
        String plainText = "plainPassword";
        String plainResult = encryptionService.decryptIfEncrypted(plainText);
        assertEquals(plainText, plainResult);

        // Test with null
        String nullResult = encryptionService.decryptIfEncrypted(null);
        assertNull(nullResult);

        // Test with empty string
        String emptyResult = encryptionService.decryptIfEncrypted("");
        assertEquals("", emptyResult);
    }

    @Test
    public void testTokenHashing() {
        // Skip test if service is not available
        if (encryptionService == null) {
            return;
        }
        
        String token = "test-token-123";
        String hash1 = encryptionService.hashToken(token);
        String hash2 = encryptionService.hashToken(token);
        
        // Same token should produce same hash (deterministic)
        assertEquals(hash1, hash2);
        
        // Hash should be 64 characters (SHA-256 hex)
        assertEquals(64, hash1.length());
        
        // Hash should be different from original token
        assertNotEquals(token, hash1);
        
        // Hash should be lowercase hex
        assertTrue(hash1.matches("^[a-f0-9]{64}$"));
    }

    @Test
    public void testTokenHashingWithRoom() {
        if (encryptionService == null) {
            return;
        }
        
        String token = "test-token-123";
        String roomId1 = "room1";
        String roomId2 = "room2";
        
        String hash1 = encryptionService.hashTokenWithRoom(token, roomId1);
        String hash2 = encryptionService.hashTokenWithRoom(token, roomId2);
        
        // Same token with different rooms should produce different hashes
        assertNotEquals(hash1, hash2);
        
        // Same token with same room should produce same hash
        String hash1Repeat = encryptionService.hashTokenWithRoom(token, roomId1);
        assertEquals(hash1, hash1Repeat);
    }

    @Test
    public void testTokenValidation() {
        if (encryptionService == null) {
            return;
        }
        
        String token = "test-token-456";
        String hash = encryptionService.hashToken(token);
        
        // Valid token should validate
        assertTrue(encryptionService.validateToken(token, hash));
        
        // Invalid token should not validate
        assertFalse(encryptionService.validateToken("wrong-token", hash));
        
        // Null inputs should not validate
        assertFalse(encryptionService.validateToken(null, hash));
        assertFalse(encryptionService.validateToken(token, null));
    }

    @Test
    public void testTokenValidationWithRoom() {
        if (encryptionService == null) {
            return;
        }
        
        String token = "test-token-789";
        String roomId = "test-room";
        String hash = encryptionService.hashTokenWithRoom(token, roomId);
        
        // Valid token and room should validate
        assertTrue(encryptionService.validateTokenWithRoom(token, roomId, hash));
        
        // Wrong token should not validate
        assertFalse(encryptionService.validateTokenWithRoom("wrong-token", roomId, hash));
        
        // Wrong room should not validate
        assertFalse(encryptionService.validateTokenWithRoom(token, "wrong-room", hash));
    }

    @Test
    public void testIsTokenHash() {
        if (encryptionService == null) {
            return;
        }
        
        String token = "test-token";
        String hash = encryptionService.hashToken(token);
        
        // Hash should be recognized as hash
        assertTrue(encryptionService.isTokenHash(hash));
        
        // Plain token should not be recognized as hash
        assertFalse(encryptionService.isTokenHash(token));
        
        // Short strings should not be recognized as hash
        assertFalse(encryptionService.isTokenHash("short"));
        
        // Non-hex strings should not be recognized as hash
        assertFalse(encryptionService.isTokenHash("g123456789012345678901234567890123456789012345678901234567890123"));
        
        // Null should not be recognized as hash
        assertFalse(encryptionService.isTokenHash(null));
    }

    @Test
    public void testHashTokenErrorHandling() {
        if (encryptionService == null) {
            return;
        }
        
        // Null token should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.hashToken(null);
        });
        
        // Empty token should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.hashToken("");
        });
    }

    @Test
    public void testHashTokenWithRoomErrorHandling() {
        if (encryptionService == null) {
            return;
        }
        
        // Null token should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.hashTokenWithRoom(null, "room1");
        });
        
        // Null room should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.hashTokenWithRoom("token", null);
        });
        
        // Empty inputs should throw exceptions
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.hashTokenWithRoom("", "room1");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.hashTokenWithRoom("token", "");
        });
    }
}