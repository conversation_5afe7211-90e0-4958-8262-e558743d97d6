package hk.org.ha.sc3.sybasechatops.service;

import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import lombok.extern.slf4j.Slf4j;

/**
 * Service class for string encryption and decryption using Jasypt
 * Replaces the legacy StringEncryptionUtil
 */
@Slf4j
@Service
public class JasyptEncryptionService {

    private static final String ENCRYPTION_PREFIX = "ENC(";
    private static final String ENCRYPTION_SUFFIX = ")";
    private static final String TOKEN_HASH_ALGORITHM = "SHA-256";
    private static final String DEFAULT_TOKEN_SALT = "chatops-default-salt";

    @Autowired
    @Qualifier("jasyptStringEncryptor")
    private StringEncryptor stringEncryptor;

    @Value("${jasypt.encryptor.password:" + DEFAULT_TOKEN_SALT + "}")
    private String tokenSalt;

    /**
     * Encrypts a string using Jasypt
     *
     * @param plainText The text to encrypt
     * @return Encrypted string with ENC(xxxxx) format
     */
    public String encrypt(String plainText) {
        try {
            String encrypted = stringEncryptor.encrypt(plainText);
            return ENCRYPTION_PREFIX + encrypted + ENCRYPTION_SUFFIX;
        } catch (Exception e) {
            log.error("Encryption failed", e);
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Encrypts a JSON object using Jasypt
     *
     * @param jsonObj The object to encrypt as JSON string
     * @return Encrypted string with ENC(xxxxx) format
     */
    public String encryptJson(Object jsonObj) {
        if (jsonObj == null) {
            throw new NullPointerException("JSON object cannot be null");
        }
        
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonStr = objectMapper.writeValueAsString(jsonObj);
            return encrypt(jsonStr);
        } catch (JsonProcessingException e) {
            log.error("JSON processing failed", e);
            throw new RuntimeException("Error processing JSON", e);
        }
    }

    /**
     * Encrypts a JsonNode object using Jasypt
     *
     * @param jsonNode The JsonNode to encrypt
     * @return Encrypted string with ENC(xxxxx) format
     */
    public String encryptJsonNode(JsonNode jsonNode) {
        if (jsonNode == null) {
            throw new NullPointerException("JsonNode cannot be null");
        }
        
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonStr = objectMapper.writeValueAsString(jsonNode);
            return encrypt(jsonStr);
        } catch (JsonProcessingException e) {
            log.error("JSON processing failed", e);
            throw new RuntimeException("Error processing JSON", e);
        }
    }

    /**
     * Decrypts a Jasypt encrypted string
     *
     * @param encryptedText Encrypted text in ENC(xxxxx) format
     * @return The decrypted plaintext
     */
    public String decrypt(String encryptedText) {
        try {
            // Extract the Base64 content from ENC(xxxxx) format
            String base64Content = extractBase64Content(encryptedText);
            return stringEncryptor.decrypt(base64Content);
        } catch (Exception e) {
            log.error("Decryption failed", e);
            throw new RuntimeException("Decryption failed", e);
        }
    }

    /**
     * Decrypts an encrypted JSON string and returns it as a HashMap object
     *
     * @param encryptedJson Encrypted JSON string in ENC(xxxxx) format
     * @return HashMap containing the decrypted JSON data
     */
    public java.util.HashMap<String, String> decryptJson(String encryptedJson) {
        if (encryptedJson == null || encryptedJson.isEmpty()) {
            throw new IllegalArgumentException("Encrypted JSON string cannot be null or empty");
        }
        
        try {
            String jsonStr = decrypt(encryptedJson);
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonStr, new TypeReference<java.util.HashMap<String, String>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON processing failed during decryption", e);
            throw new RuntimeException("Error processing JSON during decryption", e);
        }
    }

    /**
     * Decrypts an encrypted JSON string and returns it as a JsonNode object
     *
     * @param encryptedJson Encrypted JSON string in ENC(xxxxx) format
     * @return JsonNode containing the decrypted JSON data
     */
    public JsonNode decryptJsonAsNode(String encryptedJson) {
        if (encryptedJson == null || encryptedJson.isEmpty()) {
            throw new IllegalArgumentException("Encrypted JSON string cannot be null or empty");
        }
        
        try {
            String jsonStr = decrypt(encryptedJson);
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            log.error("JSON processing failed during decryption to JsonNode", e);
            throw new RuntimeException("Error processing JSON during decryption to JsonNode", e);
        }
    }

    /**
     * Extracts the Base64 content from encrypted text in ENC(xxxxx) format
     *
     * @param encryptedText The encrypted text in ENC(xxxxx) format
     * @return The Base64 content without the ENC() wrapper
     * @throws IllegalArgumentException if the format is invalid
     */
    private String extractBase64Content(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            throw new IllegalArgumentException("Encrypted text cannot be null or empty");
        }
        
        if (!encryptedText.startsWith(ENCRYPTION_PREFIX) || !encryptedText.endsWith(ENCRYPTION_SUFFIX)) {
            throw new IllegalArgumentException("Invalid encrypted text format. Expected format: ENC(xxxxx)");
        }
        
        return encryptedText.substring(ENCRYPTION_PREFIX.length(), encryptedText.length() - ENCRYPTION_SUFFIX.length());
    }

    /**
     * Checks if a string is in encrypted format (starts with ENC( and ends with ))
     *
     * @param text The text to check
     * @return true if the text is in encrypted format, false otherwise
     */
    public boolean isEncrypted(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        return text.startsWith(ENCRYPTION_PREFIX) && text.endsWith(ENCRYPTION_SUFFIX);
    }

    /**
     * Decrypts a string if it's encrypted, otherwise returns the original string.
     * This is a convenience method for the common pattern of conditional decryption.
     *
     * @param text The text that may or may not be encrypted
     * @return The decrypted text if encrypted, or the original text if not encrypted
     */
    public String decryptIfEncrypted(String text) {
        if (text == null) {
            return null;
        }
        return isEncrypted(text) ? decrypt(text) : text;
    }

    /**
     * Hashes a token using SHA-256 with application-specific salt
     * This method creates a deterministic hash for token comparison
     *
     * @param token The plaintext token to hash
     * @return The SHA-256 hash of the token as a hexadecimal string
     */
    public String hashToken(String token) {
        if (token == null || token.isEmpty()) {
            throw new IllegalArgumentException("Token cannot be null or empty");
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(TOKEN_HASH_ALGORITHM);
            String saltedToken = tokenSalt + token;
            byte[] hashBytes = digest.digest(saltedToken.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256 algorithm not available", e);
            throw new RuntimeException("Token hashing failed: SHA-256 not available", e);
        } catch (Exception e) {
            log.error("Token hashing failed", e);
            throw new RuntimeException("Token hashing failed", e);
        }
    }

    /**
     * Hashes a token with room-specific salt for additional security
     * This creates a unique hash per room for the same token
     *
     * @param token The plaintext token to hash
     * @param roomId The room ID to include in the hash
     * @return The SHA-256 hash of the token with room-specific salt
     */
    public String hashTokenWithRoom(String token, String roomId) {
        if (token == null || token.isEmpty()) {
            throw new IllegalArgumentException("Token cannot be null or empty");
        }
        if (roomId == null || roomId.isEmpty()) {
            throw new IllegalArgumentException("Room ID cannot be null or empty");
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(TOKEN_HASH_ALGORITHM);
            String saltedToken = tokenSalt + token + roomId;
            byte[] hashBytes = digest.digest(saltedToken.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256 algorithm not available", e);
            throw new RuntimeException("Token hashing failed: SHA-256 not available", e);
        } catch (Exception e) {
            log.error("Token hashing with room failed", e);
            throw new RuntimeException("Token hashing with room failed", e);
        }
    }

    /**
     * Validates a token by comparing its hash with the stored hash
     * Uses constant-time comparison to prevent timing attacks
     *
     * @param plainToken The plaintext token to validate
     * @param storedHash The stored hash to compare against
     * @return true if the token is valid, false otherwise
     */
    public boolean validateToken(String plainToken, String storedHash) {
        if (plainToken == null || storedHash == null) {
            return false;
        }

        try {
            String computedHash = hashToken(plainToken);
            return constantTimeEquals(computedHash, storedHash);
        } catch (Exception e) {
            log.error("Token validation failed", e);
            return false;
        }
    }

    /**
     * Validates a token with room-specific hashing
     *
     * @param plainToken The plaintext token to validate
     * @param roomId The room ID for room-specific hashing
     * @param storedHash The stored hash to compare against
     * @return true if the token is valid, false otherwise
     */
    public boolean validateTokenWithRoom(String plainToken, String roomId, String storedHash) {
        if (plainToken == null || roomId == null || storedHash == null) {
            return false;
        }

        try {
            String computedHash = hashTokenWithRoom(plainToken, roomId);
            return constantTimeEquals(computedHash, storedHash);
        } catch (Exception e) {
            log.error("Token validation with room failed", e);
            return false;
        }
    }

    /**
     * Converts byte array to hexadecimal string
     *
     * @param bytes The byte array to convert
     * @return Hexadecimal string representation
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * Constant-time string comparison to prevent timing attacks
     *
     * @param a First string to compare
     * @param b Second string to compare
     * @return true if strings are equal, false otherwise
     */
    private boolean constantTimeEquals(String a, String b) {
        if (a.length() != b.length()) {
            return false;
        }

        byte[] aBytes = a.getBytes(StandardCharsets.UTF_8);
        byte[] bBytes = b.getBytes(StandardCharsets.UTF_8);
        
        int result = 0;
        for (int i = 0; i < aBytes.length; i++) {
            result |= aBytes[i] ^ bBytes[i];
        }
        
        return result == 0;
    }

    /**
     * Checks if a token appears to be a hash (64 character hex string for SHA-256)
     *
     * @param token The token to check
     * @return true if the token appears to be a SHA-256 hash, false otherwise
     */
    public boolean isTokenHash(String token) {
        if (token == null || token.length() != 64) {
            return false;
        }
        return token.matches("^[a-f0-9]{64}$");
    }
}