package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsNonDbProduct;


public interface ChatopsNonDbProductRepository extends CrudRepository<ChatopsNonDbProduct, String> {
    List<ChatopsNonDbProduct> findAll();

    List<ChatopsNonDbProduct> findByInstance(String instance);

    @Query("SELECT host FROM ChatopsNonDbProduct WHERE instance = :instance")
    List<String> findHostByInstance(String instance);

}
