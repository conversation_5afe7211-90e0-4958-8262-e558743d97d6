package hk.org.ha.sc3.sybasechatops;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableScheduling
@EnableConfigurationProperties
@ConfigurationPropertiesScan("hk.org.ha.sc3.sybasechatops.config")
@SpringBootApplication
public class SybaseChatopsApplication {

	public static void main(String[] args) {
		SpringApplication.run(SybaseChatopsApplication.class, args);
	}

}
