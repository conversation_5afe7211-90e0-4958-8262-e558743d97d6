package hk.org.ha.sc3.sybasechatops.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import hk.org.ha.sc3.sybasechatops.config.ClouderaConfig;
import hk.org.ha.sc3.sybasechatops.constant.ClouderaRoleEnum;
import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaCluster;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaComponent;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaClusterRepository;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaComponentRepository;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
@Service
public class ClouderaService implements ICommand {

        private static final int WINDOW_WIDTH = 1600;
        private static final int WINDOW_HEIGHT = 900;

        private HttpClient sslHttpClient;
        private ClouderaComponentRepository clouderaComponentRepository;
        private ClouderaClusterRepository clouderaClusterRepository;
        private JasyptEncryptionService jasyptEncryptionService;
        private WebDriverService webDriverService;
        private ClouderaConfig clouderaConfig;

        public ClouderaService(HttpClient sslHttpClient,
                        ClouderaComponentRepository clouderaComponentRepository,
                        ClouderaClusterRepository clouderaClusterRepository,
                        JasyptEncryptionService jasyptEncryptionService,
                        WebDriverService webDriverService,
                        ClouderaConfig clouderaConfig) {
                this.sslHttpClient = sslHttpClient;
                this.clouderaComponentRepository = clouderaComponentRepository;
                this.clouderaClusterRepository = clouderaClusterRepository;
                this.jasyptEncryptionService = jasyptEncryptionService;
                this.webDriverService = webDriverService;
                this.clouderaConfig = clouderaConfig;
        }

        /* Cloudera Screenshot methods */
        private String navigateDashboard(FirefoxDriver driver, ClouderaCluster clouderaCluster) {
                String url = String.format("https://%s:%s", clouderaCluster.getApiHost(), 7183);
                driver.navigate().to(url);
                return url;
        }

        private void login(FirefoxDriver driver, ClouderaCluster clouderaCluster) {
                WebElement userBox = driver.findElement(By.name("j_username"));
                WebElement passwordBox = driver.findElement(By.name("j_password"));
                WebElement loginButton = driver.findElement(By.xpath("//*[@id=\"loginForm\"]/div/div[4]/div/button"));

                final String password = jasyptEncryptionService.decryptIfEncrypted(clouderaCluster.getPassword());

                userBox.sendKeys(clouderaCluster.getUser());
                passwordBox.sendKeys(password);
                try {
                        Thread.sleep(1000);
                } catch (Exception e) {}
                loginButton.click();
        }

        private void setWindowToDashboardHeight(FirefoxDriver driver) {
                driver.manage().window()
                        .setSize(new Dimension(WINDOW_WIDTH, WINDOW_HEIGHT));
        }

        private String getCurrentUrlSafely(FirefoxDriver driver, String fallbackUrl) {
                try {
                        return driver.getCurrentUrl();
                } catch (Exception e) {
                        log.warn("Could not get current URL after screenshot: {}", e.getMessage());
                        return fallbackUrl;
                }
        }

        public CommandResult takeHomeScreenshot(String clusterName) throws Exception {
                FirefoxDriver driver = null;
                long hcStartTime = System.currentTimeMillis();
                long hcEndTime;
                String file = "";
                String currentUrl = "";
                String finalUrl = "";
                double elapsedTime = 0;
                try {
                        driver = webDriverService.firefoxDriver();
                        ClouderaCluster clouderaCluster = clouderaClusterRepository.findByClusterName(clusterName).get();
                        currentUrl = navigateDashboard(driver, clouderaCluster);
                        login(driver, clouderaCluster);
                        setWindowToDashboardHeight(driver);
                        Thread.sleep(clouderaConfig.getScreenshotDelayMs());
                        file = webDriverService.takeSnapShot(driver);
                        finalUrl = getCurrentUrlSafely(driver, currentUrl);
                        log.info("Cloudera command executed successfully. Final URL: {}", finalUrl);
                        hcEndTime = System.currentTimeMillis();
                        elapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                } catch (Exception e) {
                        log.error("Cloudera Capture ERROR: ", e);
                } finally {
                        if (driver != null) {
                             driver.quit();
                        }
                }
                return CommandResult.builder()
                                .file(file)
                                .command(finalUrl.isEmpty() ? currentUrl : finalUrl)
                                .elapsedTime(elapsedTime)
                                .returnType(CmdReturnEnum.IMAGE)
                                .pdfTitle("Cloudera Manager Home Capture")
                                .build();
        }

        /* API call methods */
        private WebClient getWebClient(ClouderaCluster cluster) {
                String apiBaseUrl = String.format("https://%s:7183/api/v%s", cluster.getApiHost(),
                                cluster.getApiVersion());

                final String password = jasyptEncryptionService.decryptIfEncrypted(cluster.getPassword());

                return WebClient.builder()
                                .baseUrl(apiBaseUrl)
                                .defaultHeaders(headers -> headers.setBasicAuth(
                                                cluster.getUser(),
                                                password))
                                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient))
                                .build();
        }

        private Mono<String> callRoleCommand(WebClient webClient, String clusterName, String serviceName,
                        String command, String roleId) {
                String path = String.format("/clusters/%s/services/%s/roleCommands/%s", clusterName, serviceName,
                                command);

                String requestBody = String.format("{\"items\":[\"%s\"]}", roleId);

                return webClient.post()
                                .uri(path)
                                .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                                .bodyValue(requestBody)
                                .retrieve()
                                .bodyToMono(String.class);
        }

        private Mono<String> chkRoleCommand(WebClient webClient, String clusterName, String serviceName) {
                String path = String.format("/clusters/%s/services/%s/roles", clusterName, serviceName);

                return webClient.get()
                                .uri(path)
                                .retrieve()
                                .bodyToMono(String.class);
        }

        /* RepositoryHelper methods */
        public String[] getDistinctHost(ClouderaRoleEnum role, String clusterName) {
                List<ClouderaComponent> components = this.clouderaComponentRepository
                                .findDistinctByRoleAndClusterClusterName(role, clusterName);
                return components.stream()
                                .map(component -> component.getId().getHost()) // Extracting id.host
                                .toArray(String[]::new); // Collecting to String[]
        }

        public String[] getDistinctClusterName() {
                return this.clouderaClusterRepository.findAll().stream().map(c -> c.getClusterName())
                                .toArray(String[]::new);
        }

        public String[] getDistinctRole() {
                return this.clouderaComponentRepository.findDistinctRole().stream().toArray(String[]::new);
        }

        public ClouderaComponent getClouderaComponent(String host, ClouderaRoleEnum role) {
                ClouderaComponent component = clouderaComponentRepository.findByIdHostAndRole(host, role)
                                .orElseThrow(() -> new RuntimeException("Cloudrea component not found"));
                return component;
        }

        @Override
        public CommandResult execByCommand(Command command) throws Exception {

                String apiResult = "";
                WebClient webClient;
                HashMap<String, String> storeVariable = command.getStoreVariables();

                switch (command.getId()) {
                        case "CLOUDERA_API_CHK_ROLE":
                                String service = storeVariable.get(CmdArgEnum.ARG_CDP_SERVICE.name());
                                String clusterName = storeVariable.get(CmdArgEnum.ARG_CDP_CLUSTER.name());
                                ClouderaCluster cluster = clouderaClusterRepository
                                                .findByClusterName(clusterName).orElseThrow(() -> new RuntimeException(
                                                                "Cloudera cluster name not found"));

                                webClient = this.getWebClient(cluster);
                                apiResult = this.chkRoleCommand(webClient, clusterName,
                                                service.toLowerCase()).block();
                                apiResult = Arrays.stream(apiResult.split("\n"))
                                                .filter(line -> line.contains("entityStatus") || line
                                                                .contains("hostname") || line.contains("roleUrl"))
                                                .collect(Collectors.joining("\n"));
                                break;
                        case "CLOUDERA_API_RESTART":
                        case "CLOUDERA_API_START":
                        case "CLOUDERA_API_STOP":
                                String host = storeVariable.get(CmdArgEnum.ARG_CDP_HOST.name());
                                String instance = storeVariable.get(CmdArgEnum.ARG_CDP_INSTANCE.name());
                                ClouderaComponent component = clouderaComponentRepository
                                                .findByIdHostAndIdInstance(host, instance)
                                                .orElseThrow(() -> new RuntimeException(
                                                                "Cloudera component not found"));
                                webClient = this.getWebClient(component.getCluster());

                                apiResult = this.callRoleCommand(webClient, component.getCluster().getClusterName(),
                                                component.getRole().toString().toLowerCase(),
                                                command.getCommand().toLowerCase(),
                                                component.getId().getInstance()).block();
                                break;
                        default:
                                apiResult = "";
                }
                log.info("Cloudera API command executed: {}", command.getCommand());
                return CommandResult.builder().stdout(apiResult).stderr("").rc(0).build();
        }

        @Override
        public CmdTypeEnum getCmdType() throws Exception {
                return CmdTypeEnum.CLOUDERA_API;
        }
}