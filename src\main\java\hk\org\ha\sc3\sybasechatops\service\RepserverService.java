package hk.org.ha.sc3.sybasechatops.service;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Service;
import hk.org.ha.sc3.sybasechatops.repository.ChatopsNonDbProductRepository;
import hk.org.ha.sc3.sybasechatops.repository.RepDbSubRepository;
import hk.org.ha.sc3.sybasechatops.model.db.ChatopsNonDbProduct;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.db.RepDbSub;
import hk.org.ha.sc3.sybasechatops.model.db.id.RepDbSubId;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class RepserverService {
    private final SshService sshService;
    private final ChatopsNonDbProductRepository chatopsNonDbProductRepository;
    private final RepDbSubRepository repDbSubRepository;

    public RepserverService(SshService sshService, ChatopsNonDbProductRepository chatopsNonDbProductRepository, RepDbSubRepository repDbSubRepository) {
        this.sshService = sshService;
        this.chatopsNonDbProductRepository = chatopsNonDbProductRepository;
        this.repDbSubRepository = repDbSubRepository;
    }

    public CommandResult runCommandOnRepServer() throws IOException {
        log.info("Running command on host cdcdev05...");
        return sshService.exec("rsmaint", "cdcdev05", "ls /appl/rs/home/<USER>/ckl456/_old", null);
    }
    
    public CommandResult getAllHostAndRs() throws IOException {
        log.info("Running command on host cdcdev05...");
        return sshService.exec("rsmaint", "cdcdev05", "cat /appl/rs/home/<USER>/elzam/bin/cronjob/_out/get_host-rs_all.txt", null);
    }

    public CommandResult getAllRsConn() throws IOException {
        log.info("Running command on host cdcdev05...");
        return sshService.exec("rsmaint", "cdcdev05", "cat /appl/rs/home/<USER>/elzam/bin/cronjob/_out/get_rs-conn_all.txt", null);
    }
    
    public List<ChatopsNonDbProduct> getAllNonDbProduct() {
        List<ChatopsNonDbProduct> result = this.chatopsNonDbProductRepository.findAll();
        log.info("Result of Non Db product [{}].", result);
        return result;
    }

    public List<ChatopsNonDbProduct> getRecordByInstance(String instance) {
        return this.chatopsNonDbProductRepository.findByInstance(instance);
    }

    public List<String> findHostByInstance(String instance) {
        return this.chatopsNonDbProductRepository.findHostByInstance(instance);
    }

    public List<RepDbSub> checkDbSubExist(String rds, String rdb) {
        return this.repDbSubRepository.findByIdRdsAndIdRdb(rds,rdb);
    }

 
    public Mono<String> insertRepserverRecord(String host, String instance) {
        return Mono.fromCallable(() -> {
            ChatopsNonDbProduct newRecord = new ChatopsNonDbProduct();
            newRecord.setHost(host);
            newRecord.setInstance(instance);
            newRecord.setType("REPSERVER");
            newRecord.setTeam("SC3");
    
            chatopsNonDbProductRepository.save(newRecord);
            log.info("Inserted new record: host={}, instance={}, type=REPSERVER, team=SC3", host, instance);
            return "Inserted: " + instance;
        });
    }
    
    public Flux<String> updateAllNonDbProduct() {
        return Mono.fromCallable(() -> getAllHostAndRs().getStdout())
            .flatMapMany(output -> Flux.fromArray(output.split("\n"))
                .map(String::trim)
                .filter(line -> !line.isEmpty())
                .map(line -> line.split("\\s+"))
                .filter(parts -> parts.length == 2))
            .flatMap(parts -> {
                String host = parts[0];
                String instance = parts[1];
                List<ChatopsNonDbProduct> records = getRecordByInstance(instance);
                if (records.isEmpty()) {
                    return insertRepserverRecord(host, instance)
                        .map(result -> "Inserted: " + instance)
                        .onErrorResume(e -> Mono.just("Error inserting " + instance + ": " + e.getMessage()));
                } else {
                    return Mono.just("Skipped: " + instance);
                }
            });
    }
    
    public Mono<String> insertRepDbSub(String instance, String rds, String rdb) {
        return Mono.fromCallable(() -> {
            List<String> hosts = findHostByInstance(instance);
            String host = "";
            if (!hosts.isEmpty()) {
                host = hosts.get(0);
            }
            RepDbSub newRecord = new RepDbSub();
            RepDbSubId id = new RepDbSubId();
            id.setHost(host);
            id.setInstance(instance);
            id.setRds(rds);
            id.setRdb(rdb);
            newRecord.setId(id);
            repDbSubRepository.save(newRecord);
            log.info("Inserted new RepDbSub record: host={}, instance={}, rds={}, rdb={}", host, instance, rds, rdb);
            return "Inserted: " + host + " " + instance + " " + rds + " " + rdb;
        });
    }

    public Flux<String> updateAllDbSub() {
        return Mono.fromCallable(() -> getAllRsConn().getStdout())
            .flatMapMany(output -> Flux.fromArray(output.split("\n"))
                .map(String::trim)
                .filter(line -> !line.isEmpty())
                .map(line -> line.split("\\s+"))
                .filter(parts -> parts.length >= 3))
            .flatMap(parts -> {
                String instance = parts[0];
                String rds = parts[1];
                String rdb = parts[2];
                List<RepDbSub> existingRecords = checkDbSubExist(rds, rdb);
                if (!existingRecords.isEmpty()) {
                    return Mono.just("Skipped: " + instance + " " + rds + " " + rdb);
                }
                return insertRepDbSub(instance, rds, rdb)
                    .map(result -> "Inserted: " + instance + " " + rds + " " + rdb)
                    .onErrorResume(e -> Mono.just("Error inserting " + instance + ": " + e.getMessage()));
            });
    }
    
}

