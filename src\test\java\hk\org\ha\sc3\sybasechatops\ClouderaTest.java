package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.*;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.imageio.ImageIO;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.service.ClouderaService;

@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class ClouderaTest {

    @Autowired
	private ClouderaService clouderaService;

	@Test
	void testGetClouderaScreenshot() throws Exception {
		// Execute the screenshot method
		CommandResult result = clouderaService.takeHomeScreenshot("aida_dev01");

		// Verify the result is not null
		assertNotNull(result, "CommandResult should not be null");

		// Verify the file path is not null or empty
		assertNotNull(result.getFile(), "File path should not be null");
		assertFalse(result.getFile().isEmpty(), "File path should not be empty");

		// Verify the file actually exists
		File screenshotFile = new File(result.getFile());
		assertTrue(screenshotFile.exists(), "Screenshot file should exist at: " + result.getFile());

		// Verify it's a file (not a directory)
		assertTrue(screenshotFile.isFile(), "Path should point to a file, not a directory");

		// Verify the file has content (non-zero size)
		assertTrue(screenshotFile.length() > 0, "Screenshot file should have content (size > 0)");

		// Verify it's a PNG file based on extension
		assertTrue(result.getFile().toLowerCase().endsWith(".png"), "Screenshot should be a PNG file");

		// Verify elapsed time is recorded
		assertTrue(result.getElapsedTime() >= 0, "Elapsed time should be non-negative");

		// Verify command URL is recorded
		assertNotNull(result.getCommand(), "Command URL should not be null");
		assertFalse(result.getCommand().isEmpty(), "Command URL should not be empty");

		// Verify return type is IMAGE
		assertEquals(CmdReturnEnum.IMAGE, result.getReturnType(), "Return type should be IMAGE");

		// Verify pdfTitle is not null
		assertNotNull(result.getPdfTitle(), "PDF title should not be null");
		assertFalse(result.getPdfTitle().isEmpty(), "PDF title should not be empty");

	}

	@Test
	void testGetClouderaScreenshotImageContent() throws Exception {
		// Execute the screenshot method
		CommandResult result = clouderaService.takeHomeScreenshot("aida_dev01");

		// Verify the result and file exist (basic checks)
		assertNotNull(result, "CommandResult should not be null");
		assertNotNull(result.getFile(), "File path should not be null");

		// Verify return type is IMAGE
		assertEquals(CmdReturnEnum.IMAGE, result.getReturnType(), "Return type should be IMAGE");

		// Verify pdfTitle is not null
		assertNotNull(result.getPdfTitle(), "PDF title should not be null");
		assertFalse(result.getPdfTitle().isEmpty(), "PDF title should not be empty");

		File screenshotFile = new File(result.getFile());
		assertTrue(screenshotFile.exists(), "Screenshot file should exist");

		// Verify the file is a valid image by reading it
		try {
			BufferedImage image = ImageIO.read(screenshotFile);
			assertNotNull(image, "Should be able to read the file as an image");

			// Verify image dimensions are reasonable for a screenshot
			// (Should be at least a reasonable size for a web page screenshot)
			assertTrue(image.getWidth() >= 800, "Image width should be at least 800 pixels, actual: " + image.getWidth());
			assertTrue(image.getHeight() >= 600, "Image height should be at least 600 pixels, actual: " + image.getHeight());

		} catch (IOException e) {
			fail("Should be able to read the screenshot file as a valid image: " + e.getMessage());
		}
	}
}
