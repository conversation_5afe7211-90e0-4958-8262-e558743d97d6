package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_non_db_product", schema = "health_check", catalog = "health_check")
@Getter
@Setter

public class ChatopsNonDbProduct {
    @Id
    private String instance;
    private String host;
    private String type;
    private String team;

    
}
