package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.io.IOException;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsNonDbProduct;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.db.RepDbSub;
import hk.org.ha.sc3.sybasechatops.service.RepserverService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
public class RepserverTests {
    @Autowired
    private RepserverService repserverService;

    @Test
    void testRunListCommandOnRepServer() throws IOException{
    CommandResult result = repserverService.runCommandOnRepServer();
    assertNotNull(result);
    }

    @Test
    void testGetAllHostAndRs() throws IOException{
    CommandResult result = repserverService.getAllHostAndRs();
    assertNotNull(result);
    }

    @Test
    void testGetAllRsConn() throws IOException{
    CommandResult result = repserverService.getAllRsConn();
    assertNotNull(result);
    }

    @Test
    void testGetAllNonDbProduct() {
    List<ChatopsNonDbProduct> result = repserverService.getAllNonDbProduct();
    System.out.println("Result: " + result);
    }


    @Test
    void testGetRecordByInstance() {
    List<ChatopsNonDbProduct> result = repserverService.getRecordByInstance("HA_DC106_RP3");

    System.out.println("Result: " + result);
}

    @Test
    void testGetHostByInstance() {
    List<String> result = repserverService.findHostByInstance("HA_DC106_RP3");

    System.out.println("Result: " + result);
}

    @Test
    void testCheckDbSubExist() {
    List<RepDbSub> result = repserverService.checkDbSubExist("PAS_PMI_SP11_M6", "hktest");

    System.out.println("Result: " + result);
}

 @Test
    void testInsertNewProduct() {
      String result = repserverService.insertRepserverRecord("testhost11", "TESTinstance11")
                  .block();
       System.out.println("Result: " + result);
}

@Test
void testUpdateAllNonDbProduct() {
  String result = repserverService.updateAllNonDbProduct()
              .blockLast();
   System.out.println("Result: " + result);
}

@Test
void testInsertRepDbSub() {
  String result = repserverService.insertRepDbSub("testinstance11", "TESTconnection11", "testDb11")
              .block();
   System.out.println("Result: " + result);
}

@Test
void testUpdateAllDbSub() {
  String result = repserverService.updateAllDbSub()
              .blockLast();
   System.out.println("Result: " + result);
}

}
